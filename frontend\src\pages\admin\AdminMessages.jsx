import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../../config/api';

const AdminMessages = () => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [copySuccess, setCopySuccess] = useState('');

  useEffect(() => {
    // Check authentication
    const token = sessionStorage.getItem('adminToken');
    if (!token) {
      navigate('/admin');
      return;
    }

    fetchMessages();
  }, [navigate]);

  const fetchMessages = async () => {
    try {
      const response = await axios.get(getFullApiUrl('messages'));
      setMessages(response.data);
      setLoading(false);
    } catch (err) {
      setError('Failed to load messages');
      setLoading(false);
    }
  };

  const handleMessageClick = async (message) => {
    setSelectedMessage(message);

    // Mark as read if unread
    if (message.status === 'unread') {
      try {
        await axios.put(getFullApiUrl('message', message.id), {
          status: 'read'
        });
        // Update local state
        setMessages(prev => prev.map(m =>
          m.id === message.id ? { ...m, status: 'read' } : m
        ));
      } catch (err) {
        console.error('Failed to mark message as read:', err);
      }
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess('Copied!');
      setTimeout(() => setCopySuccess(''), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopySuccess('Copied!');
      setTimeout(() => setCopySuccess(''), 2000);
    }
  };

  const generateEmailReply = (message) => {
    const subject = `Re: ${message.subject}`;
    const body = `Hi ${message.name},

Thank you for contacting Humans of Berkshire.

[Your reply here]

Best regards,
Humans of Berkshire Team

---
Original message:
From: ${message.name} <${message.email}>
Subject: ${message.subject}
Date: ${formatDate(message.createdAt)}

${message.message}`;

    return `mailto:${message.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  const handleDelete = async (messageId) => {
    if (!confirm('Are you sure you want to delete this message?')) return;
    
    try {
      await axios.delete(getFullApiUrl('message', messageId));
      setMessages(prev => prev.filter(m => m.id !== messageId));
      if (selectedMessage?.id === messageId) {
        setSelectedMessage(null);
      }
    } catch (err) {
      alert('Failed to delete message');
    }
  };

  const handleLogout = () => {
    sessionStorage.removeItem('adminToken');
    navigate('/admin');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    if (status === 'unread') {
      return `${baseClasses} bg-red-100 text-red-800`;
    } else {
      return `${baseClasses} bg-green-100 text-green-800`;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-berkshire-blue mx-auto mb-4"></div>
          <p className="text-gray-600">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-serif font-bold text-gray-900">
              Messages Management
            </h1>
            <div className="flex items-center space-x-4">
              <Link
                to="/admin/dashboard"
                className="text-berkshire-blue hover:text-blue-800"
              >
                Back to Dashboard
              </Link>
              <Link
                to="/"
                className="text-berkshire-blue hover:text-blue-800"
              >
                View Site
              </Link>
              <button
                onClick={handleLogout}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-12rem)]">
          {/* Messages List */}
          <div className="bg-white shadow rounded-lg flex flex-col">
            <div className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900">
                Messages ({messages.length})
              </h2>
            </div>

            <div className="divide-y divide-gray-200 flex-1 overflow-y-auto">
              {messages.length === 0 ? (
                <div className="p-6 text-center text-gray-500">
                  No messages yet
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    onClick={() => handleMessageClick(message)}
                    className={`p-4 cursor-pointer hover:bg-gray-50 ${
                      selectedMessage?.id === message.id ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-medium text-gray-900">{message.name}</h3>
                        <p className="text-sm text-gray-600">{message.email}</p>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <span className={getStatusBadge(message.status)}>
                          {message.status}
                        </span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(message.id);
                          }}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      {message.subject}
                    </p>
                    <p className="text-sm text-gray-600 truncate">
                      {message.message}
                    </p>
                    <p className="text-xs text-gray-500 mt-2">
                      {formatDate(message.createdAt)}
                    </p>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Message Detail */}
          <div className="bg-white shadow rounded-lg flex flex-col">
            {selectedMessage ? (
              <div className="flex flex-col h-full">
                <div className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
                  <h2 className="text-lg font-medium text-gray-900">
                    Message Details
                  </h2>
                </div>

                <div className="p-6 space-y-4 flex-1 overflow-y-auto">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">From</label>
                    <p className="text-gray-900">{selectedMessage.name}</p>
                    <p className="text-gray-600">{selectedMessage.email}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Subject</label>
                    <p className="text-gray-900">{selectedMessage.subject}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Message</label>
                    <p className="text-gray-900 whitespace-pre-wrap">{selectedMessage.message}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Received</label>
                    <p className="text-gray-600">{formatDate(selectedMessage.createdAt)}</p>
                  </div>

                  {/* Reply Actions */}
                  <div className="pt-4 border-t border-gray-200">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Reply Actions
                    </label>

                    <div className="space-y-3">
                      {/* Email Reply Button */}
                      <a
                        href={generateEmailReply(selectedMessage)}
                        className="inline-flex items-center px-4 py-2 bg-berkshire-blue text-white rounded-lg hover:bg-blue-800 transition-colors"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Open Email Client
                      </a>

                      {/* Copy Email Button */}
                      <button
                        onClick={() => copyToClipboard(selectedMessage.email)}
                        className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors ml-3"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        Copy Email
                      </button>

                      {copySuccess && (
                        <span className="text-green-600 text-sm ml-2">{copySuccess}</span>
                      )}
                    </div>

                    {/* Contact Info Display */}
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">Contact Information:</p>
                      <p className="text-sm font-medium text-gray-900">{selectedMessage.name}</p>
                      <p className="text-sm text-gray-700">{selectedMessage.email}</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-6 text-center text-gray-500 flex items-center justify-center h-full">
                <div>
                  <div className="text-4xl mb-4">📧</div>
                  <p>Select a message to view details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminMessages;
