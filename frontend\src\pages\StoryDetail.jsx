import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../config/api';
import PosterCarousel from '../components/PosterCarousel';

const StoryDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [interview, setInterview] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchInterview();
  }, [id]);

  const fetchInterview = async () => {
    try {
      const response = await axios.get(getFullApiUrl('interview', id));
      setInterview(response.data);
      setLoading(false);
    } catch (err) {
      setError('Story not found or failed to load.');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-berkshire-blue mx-auto mb-4"></div>
          <p className="text-gray-600">Loading story...</p>
        </div>
      </div>
    );
  }

  if (error || !interview) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Link to="/stories" className="btn-primary">
            Back to Stories
          </Link>
        </div>
      </div>
    );
  }

  return (
    <article className="py-12 lg:py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="flex items-center text-berkshire-blue hover:text-blue-800 mb-8 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Stories
        </button>

        {/* Header */}
        <header className="text-center mb-12">
          <div className="mb-8">
            <img
              src={interview.imageUrl || '/images/placeholder.svg'}
              alt={interview.name}
              className="w-48 h-48 md:w-64 md:h-64 object-cover rounded-full mx-auto shadow-lg"
              onError={(e) => {
                e.target.src = '/images/placeholder.svg';
              }}
            />
          </div>
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
            {interview.name}
          </h1>
          <p className="text-xl text-berkshire-blue font-medium mb-6">
            {interview.position}
          </p>
          <blockquote className="text-xl md:text-2xl text-gray-600 italic max-w-3xl mx-auto leading-relaxed">
            "{interview.quote}"
          </blockquote>
        </header>

        {/* Poster Carousel - Only show if posters exist */}
        {interview.posters && interview.posters.length > 0 && (
          <div className="mb-12">
            <PosterCarousel
              posters={interview.posters}
              className="max-w-4xl mx-auto shadow-lg"
            />
          </div>
        )}

        {/* Interview Content */}
        <div className="prose prose-lg max-w-none">
          {interview.qa && interview.qa.map((item, index) => (
            <div key={index} className="mb-8 last:mb-0">
              <h3 className="text-xl font-serif font-bold text-gray-900 mb-4">
                {item.question}
              </h3>
              <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                {item.answer}
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <footer className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="text-gray-600 mb-4 sm:mb-0">
              Interviewed by <span className="font-medium">{interview.interviewer}</span>
            </p>
            <p className="text-gray-500 text-sm">
              {new Date(interview.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
        </footer>

        {/* Navigation */}
        <div className="mt-12 text-center">
          <Link
            to="/stories"
            className="btn-secondary"
          >
            Explore More Stories
          </Link>
        </div>
      </div>
    </article>
  );
};

export default StoryDetail;
