import React from 'react';

const SocialMediaLinks = ({ socialMediaLinks, className = '', iconSize = 'w-6 h-6' }) => {
  if (!socialMediaLinks) return null;

  const { facebook, instagram, twitter } = socialMediaLinks;

  // 如果没有任何社交媒体链接，不显示组件
  if (!facebook && !instagram && !twitter) {
    return null;
  }

  const socialLinks = [
    {
      name: 'Facebook',
      url: facebook,
      icon: (
        <svg className={iconSize} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      name: 'Instagram',
      url: instagram,
      icon: (
        <svg className={iconSize} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path fillRule="evenodd" d="M12.017 0C8.396 0 7.929.01 6.71.048 5.493.087 4.73.222 4.058.42a5.916 5.916 0 0 0-2.134 1.388A5.916 5.916 0 0 0 .536 4.042C.333 4.715.198 5.478.159 6.695.122 7.913.11 8.38.11 12.017c0 3.637.011 4.104.048 5.323.039 1.217.174 1.98.372 2.652a5.916 5.916 0 0 0 1.388 2.134 5.916 5.916 0 0 0 2.134 1.388c.672.198 1.435.333 2.652.372 1.219.037 1.686.048 5.323.048 3.637 0 4.104-.011 5.323-.048 1.217-.039 1.98-.174 2.652-.372a5.916 5.916 0 0 0 2.134-1.388 5.916 5.916 0 0 0 1.388-2.134c.198-.672.333-1.435.372-2.652.037-1.219.048-1.686.048-5.323 0-3.637-.011-4.104-.048-5.323-.039-1.217-.174-1.98-.372-2.652a5.916 5.916 0 0 0-1.388-2.134A5.916 5.916 0 0 0 19.958.536c-.672-.198-1.435-.333-2.652-.372C16.087.01 15.62 0 12.017 0zm0 2.16c3.557 0 3.98.01 5.386.048 1.3.059 2.006.277 2.477.46.622.242 1.067.532 1.533.998.466.466.756.911.998 1.533.183.471.401 1.177.46 2.477.038 1.406.048 1.829.048 5.386 0 3.557-.01 3.98-.048 5.386-.059 1.3-.277 2.006-.46 2.477-.242.622-.532 1.067-.998 1.533-.466.466-.911.756-1.533.998-.471.183-1.177.401-2.477.46-1.406.038-1.829.048-5.386.048-3.557 0-3.98-.01-5.386-.048-1.3-.059-2.006-.277-2.477-.46-.622-.242-1.067-.532-1.533-.998-.466-.466-.756-.911-.998-1.533-.183-.471-.401-1.177-.46-2.477C2.17 15.997 2.16 15.574 2.16 12.017c0-3.557.01-3.98.048-5.386.059-1.3.277-2.006.46-2.477.242-.622.532-1.067.998-1.533.466-.466.911-.756 1.533-.998.471-.183 1.177-.401 2.477-.46 1.406-.038 1.829-.048 5.386-.048z" clipRule="evenodd" />
          <path fillRule="evenodd" d="M12.017 5.838a6.179 6.179 0 1 0 0 12.358 6.179 6.179 0 0 0 0-12.358zM12.017 16a3.821 3.821 0 1 1 0-7.642 3.821 3.821 0 0 1 0 7.642z" clipRule="evenodd" />
          <circle cx="18.406" cy="5.594" r="1.44" />
        </svg>
      )
    },
    {
      name: 'Twitter',
      url: twitter,
      icon: (
        <svg className={iconSize} fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
        </svg>
      )
    }
  ];

  return (
    <div className={`flex space-x-4 ${className}`}>
      {socialLinks.map((social) => {
        if (!social.url) return null;
        
        return (
          <a
            key={social.name}
            href={social.url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-gray-600 hover:text-berkshire-blue transition-colors duration-200"
            aria-label={`Visit our ${social.name} page`}
          >
            {social.icon}
          </a>
        );
      })}
    </div>
  );
};

export default SocialMediaLinks;
