// 连接管理中间件
const config = require('../config');

// 连接统计
let connectionStats = {
  total: 0,
  active: 0,
  errors: 0,
  lastError: null
};

// 连接超时中间件
const connectionTimeout = (req, res, next) => {
  // 设置请求超时
  req.setTimeout(30000, () => {
    console.warn(`⚠️ Request timeout for ${req.method} ${req.url}`);
    if (!res.headersSent) {
      res.status(408).json({ 
        error: 'Request timeout',
        message: 'The request took too long to process'
      });
    }
  });

  // 设置响应超时
  res.setTimeout(30000, () => {
    console.warn(`⚠️ Response timeout for ${req.method} ${req.url}`);
    if (!res.headersSent) {
      res.status(504).json({ 
        error: 'Gateway timeout',
        message: 'The server took too long to respond'
      });
    }
  });

  next();
};

// 连接监控中间件
const connectionMonitor = (req, res, next) => {
  connectionStats.total++;
  connectionStats.active++;

  // 记录请求开始时间
  req.startTime = Date.now();

  // 监听响应完成
  res.on('finish', () => {
    connectionStats.active--;
    const duration = Date.now() - req.startTime;
    
    // 记录慢请求
    if (duration > 5000) {
      console.warn(`🐌 Slow request: ${req.method} ${req.url} took ${duration}ms`);
    }
  });

  // 监听连接错误
  res.on('error', (error) => {
    connectionStats.active--;
    connectionStats.errors++;
    connectionStats.lastError = {
      message: error.message,
      timestamp: new Date().toISOString(),
      url: req.url,
      method: req.method
    };
    
    console.error(`❌ Connection error for ${req.method} ${req.url}:`, error.message);
  });

  // 监听客户端断开连接
  req.on('close', () => {
    if (!res.finished) {
      connectionStats.active--;
      console.warn(`⚠️ Client disconnected: ${req.method} ${req.url}`);
    }
  });

  next();
};

// Keep-Alive 优化中间件
const keepAliveOptimizer = (req, res, next) => {
  // 设置 Keep-Alive 头部
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Keep-Alive', 'timeout=65, max=1000');
  
  // 对于静态资源，设置更长的缓存
  if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/)) {
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1年
  } else if (req.url.startsWith('/api/')) {
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  } else {
    res.setHeader('Cache-Control', 'public, max-age=300'); // 5分钟
  }

  next();
};

// 错误恢复中间件
const errorRecovery = (error, req, res, next) => {
  connectionStats.errors++;
  connectionStats.lastError = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    url: req.url,
    method: req.method
  };

  console.error(`💥 Application error for ${req.method} ${req.url}:`, error);

  // 根据错误类型返回不同的响应
  if (error.code === 'ECONNRESET') {
    // 连接重置错误
    if (!res.headersSent) {
      res.status(502).json({
        error: 'Connection reset',
        message: 'The connection was reset by the client',
        retry: true
      });
    }
  } else if (error.code === 'ETIMEDOUT') {
    // 超时错误
    if (!res.headersSent) {
      res.status(504).json({
        error: 'Timeout',
        message: 'The request timed out',
        retry: true
      });
    }
  } else if (error.code === 'ENOTFOUND') {
    // DNS解析错误
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Service unavailable',
        message: 'Unable to resolve the request',
        retry: false
      });
    }
  } else {
    // 通用错误
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'production' 
          ? 'An unexpected error occurred' 
          : error.message,
        retry: true
      });
    }
  }
};

// 获取连接统计
const getConnectionStats = () => {
  return {
    ...connectionStats,
    timestamp: new Date().toISOString()
  };
};

// 重置连接统计
const resetConnectionStats = () => {
  connectionStats = {
    total: 0,
    active: 0,
    errors: 0,
    lastError: null
  };
};

module.exports = {
  connectionTimeout,
  connectionMonitor,
  keepAliveOptimizer,
  errorRecovery,
  getConnectionStats,
  resetConnectionStats
};
