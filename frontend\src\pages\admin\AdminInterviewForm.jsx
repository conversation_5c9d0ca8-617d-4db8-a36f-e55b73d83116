import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../../config/api';

const AdminInterviewForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    name: '',
    position: '',
    quote: '',
    interviewer: '',
    qa: [{ question: '', answer: '' }],
    posters: [] // 新增海报数组
  });
  const [imageFile, setImageFile] = useState(null);
  const [currentImageUrl, setCurrentImageUrl] = useState('');
  const [posterFiles, setPosterFiles] = useState([]); // 新增海报文件数组
  const [currentPosters, setCurrentPosters] = useState([]); // 当前海报URLs
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // Check authentication
    const token = sessionStorage.getItem('adminToken');
    if (!token) {
      navigate('/admin');
      return;
    }

    // Load existing interview if editing
    if (isEditing) {
      fetchInterview();
    }
  }, [id, isEditing, navigate]);

  const fetchInterview = async () => {
    try {
      const response = await axios.get(getFullApiUrl('interview', id));
      const interview = response.data;
      setFormData({
        name: interview.name,
        position: interview.position,
        quote: interview.quote,
        interviewer: interview.interviewer,
        qa: interview.qa || [{ question: '', answer: '' }],
        posters: interview.posters || []
      });
      setCurrentImageUrl(interview.imageUrl);
      setCurrentPosters(interview.posters || []);
    } catch (err) {
      setError('Failed to load interview');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleQAChange = (index, field, value) => {
    const newQA = [...formData.qa];
    newQA[index][field] = value;
    setFormData(prev => ({
      ...prev,
      qa: newQA
    }));
  };

  const addQAPair = () => {
    setFormData(prev => ({
      ...prev,
      qa: [...prev.qa, { question: '', answer: '' }]
    }));
  };

  const removeQAPair = (index) => {
    if (formData.qa.length > 1) {
      const newQA = formData.qa.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        qa: newQA
      }));
    }
  };

  const handleImageChange = (e) => {
    setImageFile(e.target.files[0]);
  };

  // 处理海报文件选择
  const handlePosterChange = (e) => {
    const files = Array.from(e.target.files);
    setPosterFiles(prev => [...prev, ...files]);
  };

  // 删除海报
  const removePoster = (index, isExisting = false) => {
    if (isExisting) {
      setCurrentPosters(prev => prev.filter((_, i) => i !== index));
    } else {
      setPosterFiles(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      let imageUrl = currentImageUrl;
      let posterUrls = [...currentPosters];

      // Upload image if a new one is selected
      if (imageFile) {
        const imageFormData = new FormData();
        imageFormData.append('image', imageFile);

        const imageResponse = await axios.post(getFullApiUrl('uploadImage'), imageFormData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        imageUrl = imageResponse.data.imageUrl;
      }

      // Upload new posters
      if (posterFiles.length > 0) {
        for (const posterFile of posterFiles) {
          const posterFormData = new FormData();
          posterFormData.append('image', posterFile);

          const posterResponse = await axios.post(getFullApiUrl('uploadImage'), posterFormData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });
          posterUrls.push(posterResponse.data.imageUrl);
        }
      }

      const interviewData = {
        ...formData,
        imageUrl,
        posters: posterUrls
      };

      if (isEditing) {
        await axios.put(getFullApiUrl('interview', id), interviewData);
      } else {
        await axios.post(getFullApiUrl('interviews'), interviewData);
      }

      navigate('/admin/dashboard');
    } catch (err) {
      setError('Failed to save interview');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-serif font-bold text-gray-900">
              {isEditing ? 'Edit Interview' : 'Add New Interview'}
            </h1>
          </div>

          {error && (
            <div className="px-6 py-4 bg-red-50 border-b border-red-200">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-2">
                  Position *
                </label>
                <input
                  type="text"
                  id="position"
                  name="position"
                  required
                  value={formData.position}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label htmlFor="interviewer" className="block text-sm font-medium text-gray-700 mb-2">
                Interviewer *
              </label>
              <input
                type="text"
                id="interviewer"
                name="interviewer"
                required
                value={formData.interviewer}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
              />
            </div>

            <div>
              <label htmlFor="quote" className="block text-sm font-medium text-gray-700 mb-2">
                Featured Quote *
              </label>
              <textarea
                id="quote"
                name="quote"
                required
                rows={3}
                value={formData.quote}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                placeholder="A compelling quote that represents this person's story..."
              />
            </div>

            {/* Image Upload */}
            <div>
              <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-2">
                Profile Image
              </label>
              {currentImageUrl && (
                <div className="mb-4">
                  <img
                    src={currentImageUrl}
                    alt="Current"
                    className="w-32 h-32 object-cover rounded-lg"
                  />
                  <p className="text-sm text-gray-500 mt-2">Current image</p>
                </div>
              )}
              <input
                type="file"
                id="image"
                accept="image/*"
                onChange={handleImageChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
              />
            </div>

            {/* Poster Carousel Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Poster Carousel (Optional)
              </label>
              <p className="text-sm text-gray-500 mb-4">
                Upload multiple poster images to display as a carousel on the interview page. Supports JPG and PNG formats.
              </p>

              {/* Current posters preview */}
              {currentPosters.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Current Posters</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {currentPosters.map((poster, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={poster}
                          alt={`Poster ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => removePoster(index, true)}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* New uploaded posters preview */}
              {posterFiles.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">New Uploaded Posters</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {posterFiles.map((file, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`New Poster ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => removePoster(index, false)}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Poster upload input */}
              <input
                type="file"
                id="posters"
                accept="image/*"
                multiple
                onChange={handlePosterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                You can select multiple files. Recommended image size is 1200x800 pixels or higher.
              </p>
            </div>

            {/* Q&A Section */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Interview Questions & Answers</h3>
                <button
                  type="button"
                  onClick={addQAPair}
                  className="bg-berkshire-blue text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors"
                >
                  Add Question
                </button>
              </div>

              {formData.qa.map((qa, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium text-gray-900">Question {index + 1}</h4>
                    {formData.qa.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeQAPair(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Remove
                      </button>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Question *
                      </label>
                      <input
                        type="text"
                        required
                        value={qa.question}
                        onChange={(e) => handleQAChange(index, 'question', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                        placeholder="Enter the interview question..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Answer *
                      </label>
                      <textarea
                        required
                        rows={4}
                        value={qa.answer}
                        onChange={(e) => handleQAChange(index, 'answer', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                        placeholder="Enter the person's response..."
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={() => navigate('/admin/dashboard')}
                className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="bg-berkshire-blue text-white px-6 py-3 rounded-lg hover:bg-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </div>
                ) : (
                  isEditing ? 'Update Interview' : 'Create Interview'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdminInterviewForm;
