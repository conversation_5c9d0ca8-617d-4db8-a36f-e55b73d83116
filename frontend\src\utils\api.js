// API 工具类 - 包含重试机制和错误处理
import axios from 'axios';
import API_CONFIG from '../config/api.js';

// 使用环境变量获取API地址
const getCurrentApiUrl = () => {
  const apiUrl = import.meta.env.VITE_API_BASE_URL || API_CONFIG.baseURL;
  console.log('🔧 axios配置 - 使用环境变量:', apiUrl);
  return apiUrl;
};

// 创建 axios 实例
const api = axios.create({
  baseURL: getCurrentApiUrl(),
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  retryDelayMultiplier: 2, // 指数退避
};

// 需要重试的错误类型
const RETRYABLE_ERRORS = [
  'ECONNREFUSED',
  'ENOTFOUND', 
  'ECONNRESET',
  'ETIMEDOUT',
  'NETWORK_ERROR',
  'ERR_CONNECTION_REFUSED',
  'ERR_CONNECTION_CLOSED',
  'ERR_CONNECTION_RESET',
];

// 判断是否为可重试的错误
const isRetryableError = (error) => {
  if (!error) return false;
  
  // 检查错误代码
  if (error.code && RETRYABLE_ERRORS.includes(error.code)) {
    return true;
  }
  
  // 检查错误消息
  if (error.message) {
    const message = error.message.toLowerCase();
    return RETRYABLE_ERRORS.some(retryableError => 
      message.includes(retryableError.toLowerCase())
    );
  }
  
  // 检查网络错误
  if (error.response === undefined && error.request) {
    return true;
  }
  
  // 检查特定的HTTP状态码
  if (error.response && error.response.status) {
    const status = error.response.status;
    return status >= 500 || status === 408 || status === 429;
  }
  
  return false;
};

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 尝试不同的API地址 (使用环境变量)
const tryDifferentApiUrl = async (config, urlIndex = 0) => {
  const fallbackUrls = [
    getCurrentApiUrl()
  ];

  if (urlIndex >= fallbackUrls.length) {
    throw new Error('所有API地址都不可用');
  }

  const baseURL = fallbackUrls[urlIndex];
  console.log(`尝试API地址 ${urlIndex + 1}/${fallbackUrls.length}: ${baseURL}`);

  try {
    // 更新axios实例的baseURL
    api.defaults.baseURL = baseURL;
    const response = await api(config);
    console.log(`✅ API地址可用: ${baseURL}`);
    return response;
  } catch (error) {
    console.warn(`❌ API地址不可用: ${baseURL}`, error.message);

    // 如果是连接错误，尝试下一个地址
    if (isRetryableError(error)) {
      return tryDifferentApiUrl(config, urlIndex + 1);
    }

    throw error;
  }
};

// 带重试的请求函数
const requestWithRetry = async (config, retryCount = 0) => {
  try {
    const response = await api(config);
    return response;
  } catch (error) {
    console.warn(`请求失败 (尝试 ${retryCount + 1}/${RETRY_CONFIG.maxRetries + 1}):`, error.message);

    // 如果是连接错误，先尝试不同的API地址
    if (isRetryableError(error) && retryCount === 0) {
      try {
        return await tryDifferentApiUrl(config);
      } catch (fallbackError) {
        console.warn('所有API地址都不可用，继续重试当前地址');
      }
    }

    // 如果不是可重试的错误，或者已达到最大重试次数，直接抛出错误
    if (!isRetryableError(error) || retryCount >= RETRY_CONFIG.maxRetries) {
      throw error;
    }

    // 计算延迟时间（指数退避）
    const delayTime = RETRY_CONFIG.retryDelay * Math.pow(RETRY_CONFIG.retryDelayMultiplier, retryCount);

    console.log(`${delayTime}ms 后重试...`);
    await delay(delayTime);

    // 递归重试
    return requestWithRetry(config, retryCount + 1);
  }
};

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      };
    }
    
    console.log(`发起请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('请求配置错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`请求成功: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    return response;
  },
  (error) => {
    // 详细的错误日志
    if (error.response) {
      console.error(`服务器错误: ${error.response.status} - ${error.response.statusText}`);
      console.error('错误数据:', error.response.data);
    } else if (error.request) {
      console.error('网络错误 - 无响应:', error.message);
      console.error('请求配置:', error.config);
    } else {
      console.error('请求配置错误:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// 导出带重试的 API 方法
export const apiRequest = {
  get: (url, config = {}) => requestWithRetry({ ...config, method: 'get', url }),
  post: (url, data, config = {}) => requestWithRetry({ ...config, method: 'post', url, data }),
  put: (url, data, config = {}) => requestWithRetry({ ...config, method: 'put', url, data }),
  delete: (url, config = {}) => requestWithRetry({ ...config, method: 'delete', url }),
};

// 健康检查函数
export const healthCheck = async () => {
  try {
    const response = await apiRequest.get('/api/health');
    return response.data;
  } catch (error) {
    console.error('健康检查失败:', error);
    throw error;
  }
};

// 连接状态监控
export class ConnectionMonitor {
  constructor() {
    this.isOnline = navigator.onLine;
    this.listeners = [];
    this.healthCheckInterval = null;
    
    // 监听网络状态变化
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }
  
  handleOnline() {
    console.log('网络连接恢复');
    this.isOnline = true;
    this.notifyListeners('online');
    this.startHealthCheck();
  }
  
  handleOffline() {
    console.log('网络连接断开');
    this.isOnline = false;
    this.notifyListeners('offline');
    this.stopHealthCheck();
  }
  
  addListener(callback) {
    this.listeners.push(callback);
  }
  
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }
  
  notifyListeners(status) {
    this.listeners.forEach(callback => callback(status));
  }
  
  startHealthCheck() {
    if (this.healthCheckInterval) return;
    
    this.healthCheckInterval = setInterval(async () => {
      try {
        await healthCheck();
        if (!this.isOnline) {
          this.handleOnline();
        }
      } catch (error) {
        if (this.isOnline) {
          console.warn('健康检查失败，可能存在连接问题');
        }
      }
    }, 30000); // 每30秒检查一次
  }
  
  stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }
  
  destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    this.stopHealthCheck();
    this.listeners = [];
  }
}

// 创建全局连接监控实例
export const connectionMonitor = new ConnectionMonitor();

// 启动健康检查
connectionMonitor.startHealthCheck();

export default api;
