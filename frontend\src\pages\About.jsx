import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getFullApiUrl } from '../config/api';

const About = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await axios.get(getFullApiUrl('settings'));
      setSettings(response.data);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };

  return (
    <div className="py-12 lg:py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-900 mb-6">
            About Our Project
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            Celebrating the stories that make our community unique
          </p>
        </div>

        {/* Main Content */}
        <div className="prose prose-lg max-w-none">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-berkshire-blue mx-auto mb-4"></div>
              <p className="text-gray-600">Loading...</p>
            </div>
          ) : (
            <div className="mb-12">
              <p className="text-lg text-gray-700 leading-relaxed mb-8">
                {settings?.aboutUsContent ||
                  "This club captures the diverse stories of our school community through photography and interviews, inspired by Humans of New York. We photograph and interview students, teachers, staff, and volunteers, sharing their experiences on the school's website and social media. This project aims to foster understanding, celebrate diversity, and create a more welcoming environment. By giving voice to various members of our community, we build connections beyond daily interactions and create a living history of our school. We believe that sharing these stories encourages new friendships, improves relationships, and strengthens our sense of community, showing that every person's story matters."
                }
              </p>
            </div>
          )}

          {/* Mission Section */}
          <section className="mb-12">
            <h2 className="text-3xl font-serif font-bold text-gray-900 mb-6">Our Mission</h2>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {settings?.missionStatement ||
                "Every person in our community has a story worth telling. Through thoughtful conversations and genuine connections, we aim to:"
              }
            </p>
            <ul className="list-disc list-inside text-lg text-gray-700 space-y-3 mb-8">
              <li>Celebrate the diversity and richness of our school community</li>
              <li>Share wisdom and experiences across different roles and backgrounds</li>
              <li>Foster deeper understanding and connection among community members</li>
              <li>Preserve the human stories that make our school special</li>
              <li>Inspire others through authentic, personal narratives</li>
            </ul>
          </section>

          {/* Process Section */}
          <section className="mb-12">
            <h2 className="text-3xl font-serif font-bold text-gray-900 mb-6">Our Process</h2>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              Each story begins with a simple conversation. We sit down with community members from 
              all walks of life - faculty, staff, students, and administrators - to learn about their 
              journeys, passions, challenges, and dreams.
            </p>
            <p className="text-lg text-gray-700 leading-relaxed mb-8">
              Through thoughtful questions and active listening, we uncover the human experiences 
              that often go unnoticed in our daily interactions. These conversations are then 
              carefully crafted into stories that honor each person's unique perspective and voice.
            </p>
          </section>

          {/* Values Section */}
          <section className="mb-12">
            <h2 className="text-3xl font-serif font-bold text-gray-900 mb-6">Our Values</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-serif font-bold text-gray-900 mb-3">Authenticity</h3>
                <p className="text-gray-700">
                  We believe in sharing genuine, unfiltered stories that reflect the real experiences 
                  and emotions of our community members.
                </p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-serif font-bold text-gray-900 mb-3">Respect</h3>
                <p className="text-gray-700">
                  Every story is treated with dignity and care, honoring the trust that individuals 
                  place in us when sharing their experiences.
                </p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-serif font-bold text-gray-900 mb-3">Inclusivity</h3>
                <p className="text-gray-700">
                  We actively seek out diverse voices and perspectives, ensuring that all members 
                  of our community feel seen and heard.
                </p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-serif font-bold text-gray-900 mb-3">Connection</h3>
                <p className="text-gray-700">
                  Through storytelling, we aim to build bridges of understanding and empathy 
                  within our community.
                </p>
              </div>
            </div>
          </section>

          {/* Get Involved Section */}
          <section className="bg-berkshire-blue text-white p-8 rounded-lg">
            <h2 className="text-3xl font-serif font-bold mb-6">Get Involved</h2>
            <p className="text-lg mb-6 opacity-90">
              Do you know someone with an inspiring story to share? Are you interested in being 
              featured or nominating someone from our community?
            </p>
            <p className="text-lg mb-8 opacity-90">
              We're always looking for new voices and perspectives to add to our collection. 
              Every person has a story worth telling, and we'd love to help share yours.
            </p>
            <a
              href="/contact"
              className="bg-white text-berkshire-blue px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-block"
            >
              Contact Us
            </a>
          </section>
        </div>
      </div>
    </div>
  );
};

export default About;
