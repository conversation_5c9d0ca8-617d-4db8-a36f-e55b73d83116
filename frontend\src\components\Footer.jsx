import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../config/api';
import SocialMediaLinks from './SocialMediaLinks';

const Footer = () => {
  const [settings, setSettings] = useState(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await axios.get(getFullApiUrl('settings'));
      setSettings(response.data);
    } catch (err) {
      // Silently fail - footer will still work without settings
    }
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <Link to="/" className="text-2xl font-serif font-bold text-white mb-4 block">
              Humans of Berkshire
            </Link>
            <p className="text-gray-300 mb-6 max-w-md">
              {settings?.websiteDescription || 
                "Discovering the stories, dreams, and wisdom of our school community through intimate conversations and shared experiences."
              }
            </p>
            
            {/* Social Media Links */}
            {settings?.socialMediaLinks &&
             (settings.socialMediaLinks.facebook || settings.socialMediaLinks.instagram || settings.socialMediaLinks.twitter) && (
              <div>
                <h4 className="text-lg font-semibold mb-3">Follow Our Stories</h4>
                <SocialMediaLinks
                  socialMediaLinks={settings.socialMediaLinks}
                  iconSize="w-6 h-6"
                  className="text-gray-300"
                />
              </div>
            )}
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/stories" className="text-gray-300 hover:text-white transition-colors">
                  Stories
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Get in Touch</h4>
            <div className="space-y-2 text-gray-300">
              {settings?.contactEmail && (
                <p>
                  <a 
                    href={`mailto:${settings.contactEmail}`}
                    className="hover:text-white transition-colors"
                  >
                    {settings.contactEmail}
                  </a>
                </p>
              )}
              {settings?.contactPhone && (
                <p>
                  <a 
                    href={`tel:${settings.contactPhone}`}
                    className="hover:text-white transition-colors"
                  >
                    {settings.contactPhone}
                  </a>
                </p>
              )}
              {settings?.contactAddress && (
                <p className="text-sm whitespace-pre-line">
                  {settings.contactAddress}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © {currentYear} Humans of Berkshire. All rights reserved.
          </p>
          <p className="text-gray-400 text-sm mt-2 sm:mt-0">
            Made with ❤️ for our community
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
