import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../config/api';

const Home = () => {
  const [interviews, setInterviews] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchInterviews();
  }, []);

  const fetchInterviews = async () => {
    try {
      const response = await axios.get(getFullApiUrl('interviews'));
      // Get the first 3 interviews for featured stories
      setInterviews(response.data.slice(0, 3));
      setLoading(false);
    } catch (err) {
      console.error('Error fetching interviews:', err);
      setLoading(false);
    }
  };

  return (
    <div className="relative">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-berkshire-blue to-blue-800 text-white">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-no-repeat"
          style={{
            backgroundImage: `url('/images/school.jpg')`,
            backgroundPosition: 'center 75%',
          }}
        ></div>
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-berkshire-blue/80 via-blue-900/70 to-blue-800/80"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-serif font-bold mb-8 tracking-tight">
              DISCOVER <span className="block text-4xl md:text-6xl font-light italic">Berkshire</span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-4xl mx-auto leading-relaxed font-light">
              By design, every person makes an impact at Berkshire—from the foundation of being known well,
              our community builds connections through shared stories, experiences, and the wisdom that comes from truly listening.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/stories"
                className="bg-white text-berkshire-blue px-10 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                EXPLORE STORIES
              </Link>
              <Link
                to="/about"
                className="border-2 border-white text-white px-10 py-4 rounded-lg font-bold text-lg hover:bg-white hover:text-berkshire-blue transition-all duration-300 transform hover:scale-105"
              >
                OUR MISSION
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* At-A-Glance Stats - Berkshire Style */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-gray-900 mb-6">
              Our <span className="text-berkshire-blue">Stories</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              The success of those in the Berkshire community extends beyond the classroom. These are our stories.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-berkshire-blue mb-2">10+</div>
              <div className="text-sm md:text-base text-gray-600 uppercase tracking-wide">Stories Shared</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-berkshire-blue mb-2">5+</div>
              <div className="text-sm md:text-base text-gray-600 uppercase tracking-wide">Departments</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-berkshire-blue mb-2">100%</div>
              <div className="text-sm md:text-base text-gray-600 uppercase tracking-wide">Community</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-berkshire-blue mb-2">∞</div>
              <div className="text-sm md:text-base text-gray-600 uppercase tracking-wide">Connections</div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Stories Preview */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-gray-900 mb-4">
              Featured Stories
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Get a glimpse into the lives and experiences of our community members
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {loading ? (
              // Loading state
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="h-48 bg-gray-200 animate-pulse"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                  </div>
                </div>
              ))
            ) : interviews.length > 0 ? (
              // Display actual interviews
              interviews.map((interview) => (
                <div key={interview.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-48 bg-gray-200 overflow-hidden">
                    {interview.imageUrl ? (
                      <img
                        src={interview.imageUrl}
                        alt={interview.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-berkshire-blue to-blue-800 flex items-center justify-center">
                        <span className="text-white text-2xl font-serif font-bold">
                          {interview.name.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="font-serif font-bold text-xl mb-2">{interview.name}</h3>
                    <p className="text-gray-600 mb-2 text-sm">{interview.position}</p>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {interview.quote || (interview.qa && interview.qa[0] ? interview.qa[0].answer.substring(0, 100) + '...' : 'Discover their amazing story...')}
                    </p>
                    <Link
                      to={`/stories/${interview.id}`}
                      className="text-berkshire-blue font-medium hover:underline"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              ))
            ) : (
              // No interviews available
              <div className="col-span-full text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold text-gray-600 mb-2">Stories Coming Soon</h3>
                <p className="text-gray-500">We're working on bringing you amazing stories from our community members.</p>
              </div>
            )}
          </div>
          
          <div className="text-center">
            <Link
              to="/stories"
              className="btn-primary inline-block"
            >
              View All Stories
            </Link>
          </div>
        </div>
      </section>

      {/* Mission Statement - Berkshire Style */}
      <section className="py-20 lg:py-32 bg-berkshire-blue text-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-serif font-bold mb-8">
                GUIDED BY OUR <span className="block text-3xl md:text-4xl font-light italic">Mission</span>
              </h2>
              <p className="text-xl leading-relaxed mb-8 font-light">
                This club captures the diverse stories of our school community through photography and interviews,
                inspired by Humans of New York. We photograph and interview students, teachers, staff, and volunteers,
                sharing their experiences on the school's website and social media.
              </p>
              <p className="text-lg leading-relaxed mb-8 opacity-90">
                This project aims to foster understanding, celebrate diversity, and create a more welcoming environment.
                By giving voice to various members of our community, we build connections beyond daily interactions
                and create a living history of our school.
              </p>
              <Link
                to="/about"
                className="bg-white text-berkshire-blue px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg inline-block"
              >
                LEARN MORE
              </Link>
            </div>
            <div className="text-center lg:text-right">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h3 className="text-2xl font-serif font-bold mb-6">Our Motto</h3>
                <blockquote className="text-xl italic font-light leading-relaxed">
                  "Every person's story matters"
                </blockquote>
                <p className="text-sm mt-4 opacity-75">
                  We believe that sharing these stories encourages new friendships,
                  improves relationships, and strengthens our sense of community.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
