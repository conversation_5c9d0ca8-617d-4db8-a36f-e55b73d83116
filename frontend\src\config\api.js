// API Configuration - 使用环境变量，便于域名修改
const getBaseURL = () => {
  // 优先使用环境变量，便于部署时修改
  const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
  console.log('🔧 API配置 - 使用环境变量:', apiUrl);
  return apiUrl;
};

const API_CONFIG = {
  // Base URL for API calls
  baseURL: getBaseURL(),

  // 备用API地址 (使用环境变量)
  fallbackURLs: [
    import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'
  ],

  // API endpoints
  endpoints: {
    // Interviews
    interviews: '/api/interviews',
    interview: (id) => `/api/interviews/${id}`,
    
    // Settings
    settings: '/api/settings',
    
    // Messages/Contact
    messages: '/api/messages',
    message: (id) => `/api/messages/${id}`,
    
    // Authentication
    auth: '/api/auth',
    
    // File upload
    uploadImage: '/api/upload-image'
  }
};

// 调试信息
console.log('🔧 API_CONFIG 初始化:', {
  baseURL: API_CONFIG.baseURL,
  environment: import.meta.env.MODE,
  isProduction: import.meta.env.PROD,
  viteApiUrl: import.meta.env.VITE_API_BASE_URL
});

// Helper function to get full API URL
export const getApiUrl = (endpoint) => {
  const fullUrl = `${API_CONFIG.baseURL}${endpoint}`;
  console.log(`🌐 API请求地址: ${fullUrl}`);
  return fullUrl;
};

// Helper function to get endpoint
export const getEndpoint = (key, ...params) => {
  const endpoint = API_CONFIG.endpoints[key];
  if (typeof endpoint === 'function') {
    return endpoint(...params);
  }
  return endpoint;
};

// Full API URL helper
export const getFullApiUrl = (key, ...params) => {
  const endpoint = getEndpoint(key, ...params);
  const fullUrl = getApiUrl(endpoint);
  console.log(`🔗 完整API地址 (${key}): ${fullUrl}`);
  return fullUrl;
};

// API故障转移功能
export const getApiUrlWithFallback = async (endpoint) => {
  const urls = [API_CONFIG.baseURL, ...API_CONFIG.fallbackURLs];

  for (const baseUrl of urls) {
    try {
      // 简单的健康检查
      const testUrl = `${baseUrl}/api/health`;
      const response = await fetch(testUrl, {
        method: 'GET',
        timeout: 5000,
        signal: AbortSignal.timeout(5000)
      });

      if (response.ok) {
        console.log(`使用API地址: ${baseUrl}`);
        return `${baseUrl}${endpoint}`;
      }
    } catch (error) {
      console.warn(`API地址不可用: ${baseUrl}`, error.message);
      continue;
    }
  }

  // 如果所有地址都不可用，返回默认地址
  console.error('所有API地址都不可用，使用默认地址');
  return `${API_CONFIG.baseURL}${endpoint}`;
};

// 智能API请求函数
export const smartApiRequest = async (endpoint, options = {}) => {
  const fullUrl = await getApiUrlWithFallback(endpoint);

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  } catch (error) {
    console.error(`API请求失败: ${fullUrl}`, error);
    throw error;
  }
};

export default API_CONFIG;
// Build timestamp: Thu Jul 24 08:39:52 PM CST 2025
