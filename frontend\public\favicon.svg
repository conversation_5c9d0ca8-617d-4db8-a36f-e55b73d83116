<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <!-- Background circle with Berkshire blue gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="personGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.9" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="48" fill="url(#bgGradient)" stroke="#1e3a8a" stroke-width="2"/>
  
  <!-- Person silhouette -->
  <g fill="url(#personGradient)">
    <!-- Head -->
    <circle cx="50" cy="32" r="12"/>
    
    <!-- Body -->
    <ellipse cx="50" cy="58" rx="16" ry="20"/>
    
    <!-- Arms -->
    <ellipse cx="35" cy="52" rx="6" ry="14" transform="rotate(-20 35 52)"/>
    <ellipse cx="65" cy="52" rx="6" ry="14" transform="rotate(20 65 52)"/>
  </g>
  
  <!-- Decorative elements representing community/connection -->
  <g fill="none" stroke="#ffffff" stroke-width="1.5" opacity="0.6">
    <!-- Connection lines -->
    <path d="M20 25 Q30 20 40 25"/>
    <path d="M60 25 Q70 20 80 25"/>
    <path d="M25 75 Q35 70 45 75"/>
    <path d="M55 75 Q65 70 75 75"/>
  </g>
  
  <!-- Small dots representing other people in community -->
  <g fill="#ffffff" opacity="0.7">
    <circle cx="25" cy="30" r="2"/>
    <circle cx="75" cy="30" r="2"/>
    <circle cx="30" cy="75" r="2"/>
    <circle cx="70" cy="75" r="2"/>
  </g>
</svg>
