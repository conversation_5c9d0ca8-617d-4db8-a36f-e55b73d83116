const rateLimit = require('express-rate-limit');
const config = require('../config');

// Store for tracking login attempts
const loginAttempts = new Map();

// General rate limiter (very generous for normal browsing)
const generalLimiter = rateLimit({
  windowMs: config.rateLimiting.general.windowMs,
  max: config.rateLimiting.general.max,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimiting.general.windowMs / 1000 / 60)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for static files and normal page loads
    return req.path.startsWith('/images/') ||
           req.path.startsWith('/css/') ||
           req.path.startsWith('/js/') ||
           req.path.endsWith('.ico') ||
           req.path.endsWith('.png') ||
           req.path.endsWith('.jpg') ||
           req.path.endsWith('.svg') ||
           req.path === '/' ||
           !req.path.startsWith('/api/');
  }
});

// API rate limiter (moderate for API endpoints)
const apiLimiter = rateLimit({
  windowMs: config.rateLimiting.api.windowMs,
  max: config.rateLimiting.api.max,
  message: {
    error: 'Too many API requests from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimiting.api.windowMs / 1000 / 60)
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Auth rate limiter (more restrictive)
const authLimiter = rateLimit({
  windowMs: config.rateLimiting.auth.windowMs,
  max: config.rateLimiting.auth.max,
  message: {
    error: 'Too many login attempts from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimiting.auth.windowMs / 1000 / 60)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Contact form rate limiter
const contactLimiter = rateLimit({
  windowMs: config.rateLimiting.contact.windowMs,
  max: config.rateLimiting.contact.max,
  message: {
    error: 'Too many contact form submissions, please wait before trying again.',
    retryAfter: Math.ceil(config.rateLimiting.contact.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // HSTS header for HTTPS
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  next();
};

// Login attempt tracking middleware
const trackLoginAttempts = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  
  // Clean up old entries
  for (const [key, value] of loginAttempts.entries()) {
    if (now - value.lastAttempt > config.auth.lockoutDuration * 60 * 1000) {
      loginAttempts.delete(key);
    }
  }
  
  // Check if IP is locked out
  const attempts = loginAttempts.get(ip);
  if (attempts && attempts.count >= config.auth.maxLoginAttempts) {
    const timeLeft = config.auth.lockoutDuration * 60 * 1000 - (now - attempts.lastAttempt);
    if (timeLeft > 0) {
      return res.status(429).json({
        error: 'Account temporarily locked due to too many failed attempts',
        retryAfter: Math.ceil(timeLeft / 1000 / 60)
      });
    } else {
      // Reset attempts after lockout period
      loginAttempts.delete(ip);
    }
  }
  
  req.loginAttempts = loginAttempts;
  next();
};

// Record failed login attempt
const recordFailedAttempt = (ip) => {
  const now = Date.now();
  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: now };
  
  attempts.count += 1;
  attempts.lastAttempt = now;
  
  loginAttempts.set(ip, attempts);
};

// Clear login attempts on successful login
const clearLoginAttempts = (ip) => {
  loginAttempts.delete(ip);
};

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Basic input sanitization
  const sanitize = (obj) => {
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        // Remove potential XSS patterns
        obj[key] = obj[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitize(obj[key]);
      }
    }
  };
  
  if (req.body) sanitize(req.body);
  if (req.query) sanitize(req.query);
  if (req.params) sanitize(req.params);
  
  next();
};

module.exports = {
  generalLimiter,
  apiLimiter,
  authLimiter,
  contactLimiter,
  securityHeaders,
  trackLoginAttempts,
  recordFailedAttempt,
  clearLoginAttempts,
  sanitizeInput
};
