import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../config/api';

const Stories = () => {
  const [interviews, setInterviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchInterviews();
  }, []);

  const fetchInterviews = async () => {
    try {
      const response = await axios.get(getFullApiUrl('interviews'));
      setInterviews(response.data);
      setLoading(false);
    } catch (err) {
      setError('Failed to load stories. Please try again later.');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-berkshire-blue mx-auto mb-4"></div>
          <p className="text-gray-600">Loading stories...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchInterviews}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="py-12 lg:py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
            Our Stories
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover the unique perspectives, experiences, and wisdom of the people who make our community special.
          </p>
        </div>

        {/* Stories Grid */}
        {interviews.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg mb-6">No stories available yet.</p>
            <p className="text-gray-500">Check back soon for inspiring stories from our community!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {interviews.map((interview) => (
              <InterviewCard key={interview.id} interview={interview} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const InterviewCard = ({ interview }) => {
  return (
    <Link
      to={`/stories/${interview.id}`}
      className="group block bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
    >
      {/* Image Section - Larger like HONY */}
      <div className="relative">
        <img
          src={interview.imageUrl || '/images/placeholder.svg'}
          alt={interview.name}
          className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
          onError={(e) => {
            e.target.src = '/images/placeholder.svg';
          }}
        />
        {/* Overlay with name - HONY style */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4">
          <h3 className="font-serif font-bold text-xl text-white mb-1">
            {interview.name}
          </h3>
          <p className="text-white/90 text-sm font-medium">
            {interview.position}
          </p>
        </div>
      </div>

      {/* Content Section - More concise like HONY */}
      <div className="p-4">
        <blockquote className="text-gray-700 text-sm leading-relaxed line-clamp-3 mb-3">
          "{interview.quote}"
        </blockquote>
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>By {interview.interviewer}</span>
          <span className="text-berkshire-blue font-medium group-hover:underline transition-colors">
            Read Story →
          </span>
        </div>
      </div>
    </Link>
  );
};

export default Stories;
