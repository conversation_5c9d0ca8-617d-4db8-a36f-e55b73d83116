import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { getFullApiUrl } from '../../config/api';

const AdminSettings = () => {
  const navigate = useNavigate();
  const [settings, setSettings] = useState({
    aboutUsContent: '',
    missionStatement: '',
    contactEmail: '',
    contactPhone: '',
    contactAddress: '',
    socialMediaLinks: {
      facebook: '',
      instagram: '',
      twitter: ''
    },
    websiteTitle: '',
    websiteDescription: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    // Check authentication
    const token = sessionStorage.getItem('adminToken');
    if (!token) {
      navigate('/admin');
      return;
    }

    fetchSettings();
  }, [navigate]);

  const fetchSettings = async () => {
    try {
      const response = await axios.get(getFullApiUrl('settings'));
      setSettings({
        aboutUsContent: response.data.aboutUsContent || '',
        missionStatement: response.data.missionStatement || '',
        contactEmail: response.data.contactEmail || '',
        contactPhone: response.data.contactPhone || '',
        contactAddress: response.data.contactAddress || '',
        socialMediaLinks: {
          facebook: response.data.socialMediaLinks?.facebook || '',
          instagram: response.data.socialMediaLinks?.instagram || '',
          twitter: response.data.socialMediaLinks?.twitter || ''
        },
        websiteTitle: response.data.websiteTitle || '',
        websiteDescription: response.data.websiteDescription || ''
      });
      setLoading(false);
    } catch (err) {
      setError('Failed to load settings');
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('social.')) {
      const socialKey = name.split('.')[1];
      setSettings(prev => ({
        ...prev,
        socialMediaLinks: {
          ...prev.socialMediaLinks,
          [socialKey]: value
        }
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      await axios.put(getFullApiUrl('settings'), settings);
      setSuccess('Settings saved successfully!');
    } catch (err) {
      setError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleLogout = () => {
    sessionStorage.removeItem('adminToken');
    navigate('/admin');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-berkshire-blue mx-auto mb-4"></div>
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-serif font-bold text-gray-900">
              Website Settings
            </h1>
            <div className="flex items-center space-x-4">
              <Link
                to="/admin/dashboard"
                className="text-berkshire-blue hover:text-blue-800"
              >
                Back to Dashboard
              </Link>
              <Link
                to="/"
                className="text-berkshire-blue hover:text-blue-800"
              >
                View Site
              </Link>
              <button
                onClick={handleLogout}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg">
          {error && (
            <div className="px-6 py-4 bg-red-50 border-b border-red-200">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {success && (
            <div className="px-6 py-4 bg-green-50 border-b border-green-200">
              <p className="text-green-600">{success}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="p-6 space-y-8">
            {/* General Settings */}
            <div>
              <h2 className="text-xl font-serif font-bold text-gray-900 mb-4">General Settings</h2>
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label htmlFor="websiteTitle" className="block text-sm font-medium text-gray-700 mb-2">
                    Website Title
                  </label>
                  <input
                    type="text"
                    id="websiteTitle"
                    name="websiteTitle"
                    value={settings.websiteTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="websiteDescription" className="block text-sm font-medium text-gray-700 mb-2">
                    Website Description
                  </label>
                  <textarea
                    id="websiteDescription"
                    name="websiteDescription"
                    rows={3}
                    value={settings.websiteDescription}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* About Us Content */}
            <div>
              <h2 className="text-xl font-serif font-bold text-gray-900 mb-4">About Us Content</h2>
              <div className="space-y-6">
                <div>
                  <label htmlFor="aboutUsContent" className="block text-sm font-medium text-gray-700 mb-2">
                    About Us Description
                  </label>
                  <textarea
                    id="aboutUsContent"
                    name="aboutUsContent"
                    rows={6}
                    value={settings.aboutUsContent}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                    placeholder="Describe your project and mission..."
                  />
                </div>

                <div>
                  <label htmlFor="missionStatement" className="block text-sm font-medium text-gray-700 mb-2">
                    Mission Statement
                  </label>
                  <textarea
                    id="missionStatement"
                    name="missionStatement"
                    rows={4}
                    value={settings.missionStatement}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                    placeholder="Your mission statement..."
                  />
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h2 className="text-xl font-serif font-bold text-gray-900 mb-4">Contact Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    id="contactEmail"
                    name="contactEmail"
                    value={settings.contactEmail}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    id="contactPhone"
                    name="contactPhone"
                    value={settings.contactPhone}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="contactAddress" className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Address
                  </label>
                  <textarea
                    id="contactAddress"
                    name="contactAddress"
                    rows={3}
                    value={settings.contactAddress}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Social Media Links */}
            <div>
              <h2 className="text-xl font-serif font-bold text-gray-900 mb-4">Social Media Links</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="facebook" className="block text-sm font-medium text-gray-700 mb-2">
                    Facebook URL
                  </label>
                  <input
                    type="url"
                    id="facebook"
                    name="social.facebook"
                    value={settings.socialMediaLinks.facebook}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                    placeholder="https://facebook.com/..."
                  />
                </div>

                <div>
                  <label htmlFor="instagram" className="block text-sm font-medium text-gray-700 mb-2">
                    Instagram URL
                  </label>
                  <input
                    type="url"
                    id="instagram"
                    name="social.instagram"
                    value={settings.socialMediaLinks.instagram}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                    placeholder="https://instagram.com/..."
                  />
                </div>

                <div>
                  <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-2">
                    Twitter URL
                  </label>
                  <input
                    type="url"
                    id="twitter"
                    name="social.twitter"
                    value={settings.socialMediaLinks.twitter}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-berkshire-blue focus:border-transparent"
                    placeholder="https://twitter.com/..."
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={saving}
                className="bg-berkshire-blue text-white px-8 py-3 rounded-lg hover:bg-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </div>
                ) : (
                  'Save Settings'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;
